#!/usr/bin/env python3
"""
Quick test to verify the server is working with correct endpoint
"""
import asyncio
import httpx
import json


async def test_server():
    async with httpx.AsyncClient() as client:
        # Test health
        print("Testing health endpoint...")
        resp = await client.get("http://localhost:8000/health")
        print(f"Health: {resp.json()}")
        
        # Test models
        print("\nTesting models endpoint...")
        resp = await client.get("http://localhost:8000/v1/models")
        models = resp.json()
        print(f"Available models: {[m['id'] for m in models['data']]}")
        
        # Test chat completion with claude-4-sonnet
        print("\nTesting chat completion with claude-4-sonnet...")
        payload = {
            "model": "claude-4-sonnet",
            "messages": [
                {"role": "user", "content": "Say hello"}
            ]
        }
        
        try:
            resp = await client.post(
                "http://localhost:8000/v1/chat/completions",
                json=payload,
                timeout=30.0
            )
            print(f"Status: {resp.status_code}")
            if resp.status_code == 200:
                data = resp.json()
                print(f"Response: {data['choices'][0]['message']['content']}")
            else:
                print(f"Error: {resp.text}")
        except Exception as e:
            print(f"Request failed: {e}")


if __name__ == "__main__":
    asyncio.run(test_server())