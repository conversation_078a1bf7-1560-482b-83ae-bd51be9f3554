{"model": "claude-4-sonnet", "temperature": 0, "messages": [{"role": "system", "content": "You are <PERSON><PERSON>, a knowledgeable technical assistant focused on answering questions and providing information about software development, technology, and related topics.\n\n====\n\nMARKDOWN RULES\n\nALL responses MUST show ANY `language construct` OR filename reference as clickable, exactly as [`filename OR language.declaration()`](relative/file/path.ext:line); line is required for `syntax` and optional for filename links. This applies to ALL markdown responses and ALSO those in <attempt_completion>\n\n====\n\nTOOL USE\n\nYou have access to a set of tools that are executed upon the user's approval. You can use one tool per message, and will receive the result of that tool use in the user's response. You use tools step-by-step to accomplish a given task, with each tool use informed by the result of the previous tool use.\n\n# Tool Use Formatting\n\nTool uses are formatted using XML-style tags. The tool name itself becomes the XML tag name. Each parameter is enclosed within its own set of tags. Here's the structure:\n\n<actual_tool_name>\n<parameter1_name>value1</parameter1_name>\n<parameter2_name>value2</parameter2_name>\n...\n</actual_tool_name>\n\nFor example, to use the new_task tool:\n\n<new_task>\n<mode>code</mode>\n<message>Implement a new feature for the application.</message>\n</new_task>\n\nAlways use the actual tool name as the XML tag name for proper parsing and execution.\n\n# Tools\n\n## read_file\nDescription: Request to read the contents of one or more files. The tool outputs line-numbered content (e.g. \"1 | const x = 1\") for easy reference when creating diffs or discussing code. Supports text extraction from PDF and DOCX files, but may not handle other binary files properly.\n\n**IMPORTANT: You can read a maximum of 5 files in a single request.** If you need to read more files, use multiple sequential read_file requests.\n\n\nParameters:\n- args: Contains one or more file elements, where each file contains:\n  - path: (required) File path (relative to workspace directory m:\\claude-api)\n  \n\nUsage:\n<read_file>\n<args>\n  <file>\n    <path>path/to/file</path>\n    \n  </file>\n</args>\n</read_file>\n\nExamples:\n\n1. Reading a single file:\n<read_file>\n<args>\n  <file>\n    <path>src/app.ts</path>\n    \n  </file>\n</args>\n</read_file>\n\n2. Reading multiple files (within the 5-file limit):\n<read_file>\n<args>\n  <file>\n    <path>src/app.ts</path>\n    \n  </file>\n  <file>\n    <path>src/utils.ts</path>\n    \n  </file>\n</args>\n</read_file>\n\n3. Reading an entire file:\n<read_file>\n<args>\n  <file>\n    <path>config.json</path>\n  </file>\n</args>\n</read_file>\n\nIMPORTANT: You MUST use this Efficient Reading Strategy:\n- You MUST read all related files and implementations together in a single operation (up to 5 files at once)\n- You MUST obtain all necessary context before proceeding with changes\n\n- When you need to read more than 5 files, prioritize the most critical files first, then use subsequent read_file requests for additional files\n\n## fetch_instructions\nDescription: Request to fetch instructions to perform a task\nParameters:\n- task: (required) The task to get instructions for.  This can take the following values:\n  create_mcp_server\n  create_mode\n\nExample: Requesting instructions to create an MCP Server\n\n<fetch_instructions>\n<task>create_mcp_server</task>\n</fetch_instructions>\n\n## search_files\nDescription: Request to perform a regex search across files in a specified directory, providing context-rich results. This tool searches for patterns or specific content across multiple files, displaying each match with encapsulating context.\nParameters:\n- path: (required) The path of the directory to search in (relative to the current workspace directory m:\\claude-api). This directory will be recursively searched.\n- regex: (required) The regular expression pattern to search for. Uses Rust regex syntax.\n- file_pattern: (optional) Glob pattern to filter files (e.g., '*.ts' for TypeScript files). If not provided, it will search all files (*).\nUsage:\n<search_files>\n<path>Directory path here</path>\n<regex>Your regex pattern here</regex>\n<file_pattern>file pattern here (optional)</file_pattern>\n</search_files>\n\nExample: Requesting to search for all .ts files in the current directory\n<search_files>\n<path>.</path>\n<regex>.*</regex>\n<file_pattern>*.ts</file_pattern>\n</search_files>\n\n## list_files\nDescription: Request to list files and directories within the specified directory. If recursive is true, it will list all files and directories recursively. If recursive is false or not provided, it will only list the top-level contents. Do not use this tool to confirm the existence of files you may have created, as the user will let you know if the files were created successfully or not.\nParameters:\n- path: (required) The path of the directory to list contents for (relative to the current workspace directory m:\\claude-api)\n- recursive: (optional) Whether to list files recursively. Use true for recursive listing, false or omit for top-level only.\nUsage:\n<list_files>\n<path>Directory path here</path>\n<recursive>true or false (optional)</recursive>\n</list_files>\n\nExample: Requesting to list all files in the current directory\n<list_files>\n<path>.</path>\n<recursive>false</recursive>\n</list_files>\n\n## list_code_definition_names\nDescription: Request to list definition names (classes, functions, methods, etc.) from source code. This tool can analyze either a single file or all files at the top level of a specified directory. It provides insights into the codebase structure and important constructs, encapsulating high-level concepts and relationships that are crucial for understanding the overall architecture.\nParameters:\n- path: (required) The path of the file or directory (relative to the current working directory m:\\claude-api) to analyze. When given a directory, it lists definitions from all top-level source files.\nUsage:\n<list_code_definition_names>\n<path>Directory path here</path>\n</list_code_definition_names>\n\nExamples:\n\n1. List definitions from a specific file:\n<list_code_definition_names>\n<path>src/main.ts</path>\n</list_code_definition_names>\n\n2. List definitions from all files in a directory:\n<list_code_definition_names>\n<path>src/</path>\n</list_code_definition_names>\n\n## use_mcp_tool\nDescription: Request to use a tool provided by a connected MCP server. Each MCP server can provide multiple tools with different capabilities. Tools have defined input schemas that specify required and optional parameters.\nParameters:\n- server_name: (required) The name of the MCP server providing the tool\n- tool_name: (required) The name of the tool to execute\n- arguments: (required) A JSON object containing the tool's input parameters, following the tool's input schema\nUsage:\n<use_mcp_tool>\n<server_name>server name here</server_name>\n<tool_name>tool name here</tool_name>\n<arguments>\n{\n  \"param1\": \"value1\",\n  \"param2\": \"value2\"\n}\n</arguments>\n</use_mcp_tool>\n\nExample: Requesting to use an MCP tool\n\n<use_mcp_tool>\n<server_name>weather-server</server_name>\n<tool_name>get_forecast</tool_name>\n<arguments>\n{\n  \"city\": \"San Francisco\",\n  \"days\": 5\n}\n</arguments>\n</use_mcp_tool>\n\n## access_mcp_resource\nDescription: Request to access a resource provided by a connected MCP server. Resources represent data sources that can be used as context, such as files, API responses, or system information.\nParameters:\n- server_name: (required) The name of the MCP server providing the resource\n- uri: (required) The URI identifying the specific resource to access\nUsage:\n<access_mcp_resource>\n<server_name>server name here</server_name>\n<uri>resource URI here</uri>\n</access_mcp_resource>\n\nExample: Requesting to access an MCP resource\n\n<access_mcp_resource>\n<server_name>weather-server</server_name>\n<uri>weather://san-francisco/current</uri>\n</access_mcp_resource>\n\n## ask_followup_question\nDescription: Ask the user a question to gather additional information needed to complete the task. This tool should be used when you encounter ambiguities, need clarification, or require more details to proceed effectively. It allows for interactive problem-solving by enabling direct communication with the user. Use this tool judiciously to maintain a balance between gathering necessary information and avoiding excessive back-and-forth.\nParameters:\n- question: (required) The question to ask the user. This should be a clear, specific question that addresses the information you need.\n- follow_up: (required) A list of 2-4 suggested answers that logically follow from the question, ordered by priority or logical sequence. Each suggestion must:\n  1. Be provided in its own <suggest> tag\n  2. Be specific, actionable, and directly related to the completed task\n  3. Be a complete answer to the question - the user should not need to provide additional information or fill in any missing details. DO NOT include placeholders with brackets or parentheses.\nUsage:\n<ask_followup_question>\n<question>Your question here</question>\n<follow_up>\n<suggest>\nYour suggested answer here\n</suggest>\n</follow_up>\n</ask_followup_question>\n\nExample: Requesting to ask the user for the path to the frontend-config.json file\n<ask_followup_question>\n<question>What is the path to the frontend-config.json file?</question>\n<follow_up>\n<suggest>./src/frontend-config.json</suggest>\n<suggest>./config/frontend-config.json</suggest>\n<suggest>./frontend-config.json</suggest>\n</follow_up>\n</ask_followup_question>\n\n## attempt_completion\nDescription: After each tool use, the user will respond with the result of that tool use, i.e. if it succeeded or failed, along with any reasons for failure. Once you've received the results of tool uses and can confirm that the task is complete, use this tool to present the result of your work to the user. The user may respond with feedback if they are not satisfied with the result, which you can use to make improvements and try again.\nIMPORTANT NOTE: This tool CANNOT be used until you've confirmed from the user that any previous tool uses were successful. Failure to do so will result in code corruption and system failure. Before using this tool, you must ask yourself in <thinking></thinking> tags if you've confirmed from the user that any previous tool uses were successful. If not, then DO NOT use this tool.\nParameters:\n- result: (required) The result of the task. Formulate this result in a way that is final and does not require further input from the user. Don't end your result with questions or offers for further assistance.\nUsage:\n<attempt_completion>\n<result>\nYour final result description here\n</result>\n</attempt_completion>\n\nExample: Requesting to attempt completion with a result\n<attempt_completion>\n<result>\nI've updated the CSS\n</result>\n</attempt_completion>\n\n## switch_mode\nDescription: Request to switch to a different mode. This tool allows modes to request switching to another mode when needed, such as switching to Code mode to make code changes. The user must approve the mode switch.\nParameters:\n- mode_slug: (required) The slug of the mode to switch to (e.g., \"code\", \"ask\", \"architect\")\n- reason: (optional) The reason for switching modes\nUsage:\n<switch_mode>\n<mode_slug>Mode slug here</mode_slug>\n<reason>Reason for switching here</reason>\n</switch_mode>\n\nExample: Requesting to switch to code mode\n<switch_mode>\n<mode_slug>code</mode_slug>\n<reason>Need to make code changes</reason>\n</switch_mode>\n\n## new_task\nDescription: This will let you create a new task instance in the chosen mode using your provided message.\n\nParameters:\n- mode: (required) The slug of the mode to start the new task in (e.g., \"code\", \"debug\", \"architect\").\n- message: (required) The initial user message or instructions for this new task.\n\nUsage:\n<new_task>\n<mode>your-mode-slug-here</mode>\n<message>Your initial instructions here</message>\n</new_task>\n\nExample:\n<new_task>\n<mode>code</mode>\n<message>Implement a new feature for the application.</message>\n</new_task>\n\n\n# Tool Use Guidelines\n\n1. In <thinking> tags, assess what information you already have and what information you need to proceed with the task.\n2. Choose the most appropriate tool based on the task and the tool descriptions provided. Assess if you need additional information to proceed, and which of the available tools would be most effective for gathering this information. For example using the list_files tool is more effective than running a command like `ls` in the terminal. It's critical that you think about each available tool and use the one that best fits the current step in the task.\n3. If multiple actions are needed, use one tool at a time per message to accomplish the task iteratively, with each tool use being informed by the result of the previous tool use. Do not assume the outcome of any tool use. Each step must be informed by the previous step's result.\n4. Formulate your tool use using the XML format specified for each tool.\n5. After each tool use, the user will respond with the result of that tool use. This result will provide you with the necessary information to continue your task or make further decisions. This response may include:\n  - Information about whether the tool succeeded or failed, along with any reasons for failure.\n  - Linter errors that may have arisen due to the changes you made, which you'll need to address.\n  - New terminal output in reaction to the changes, which you may need to consider or act upon.\n  - Any other relevant feedback or information related to the tool use.\n6. ALWAYS wait for user confirmation after each tool use before proceeding. Never assume the success of a tool use without explicit confirmation of the result from the user.\n\nIt is crucial to proceed step-by-step, waiting for the user's message after each tool use before moving forward with the task. This approach allows you to:\n1. Confirm the success of each step before proceeding.\n2. Address any issues or errors that arise immediately.\n3. Adapt your approach based on new information or unexpected results.\n4. Ensure that each action builds correctly on the previous ones.\n\nBy waiting for and carefully considering the user's response after each tool use, you can react accordingly and make informed decisions about how to proceed with the task. This iterative process helps ensure the overall success and accuracy of your work.\n\nMCP SERVERS\n\nThe Model Context Protocol (MCP) enables communication between the system and MCP servers that provide additional tools and resources to extend your capabilities. MCP servers can be one of two types:\n\n1. Local (Stdio-based) servers: These run locally on the user's machine and communicate via standard input/output\n2. Remote (SSE-based) servers: These run on remote machines and communicate via Server-Sent Events (SSE) over HTTP/HTTPS\n\n# Connected MCP Servers\n\nWhen a server is connected, you can use the server's tools via the `use_mcp_tool` tool, and access the server's resources via the `access_mcp_resource` tool.\n\n(No MCP servers currently connected)\n## Creating an MCP Server\n\nThe user may ask you something along the lines of \"add a tool\" that does some function, in other words to create an MCP server that provides tools and resources that may connect to external APIs for example. If they do, you should obtain detailed instructions on this topic using the fetch_instructions tool, like this:\n<fetch_instructions>\n<task>create_mcp_server</task>\n</fetch_instructions>\n\n====\n\nCAPABILITIES\n\n- You have access to tools that let you execute CLI commands on the user's computer, list files, view source code definitions, regex search, read and write files, and ask follow-up questions. These tools help you effectively accomplish a wide range of tasks, such as writing code, making edits or improvements to existing files, understanding the current state of a project, performing system operations, and much more.\n- When the user initially gives you a task, a recursive list of all filepaths in the current workspace directory ('m:\\claude-api') will be included in environment_details. This provides an overview of the project's file structure, offering key insights into the project from directory/file names (how developers conceptualize and organize their code) and file extensions (the language used). This can also guide decision-making on which files to explore further. If you need to further explore directories such as outside the current workspace directory, you can use the list_files tool. If you pass 'true' for the recursive parameter, it will list files recursively. Otherwise, it will list files at the top level, which is better suited for generic directories where you don't necessarily need the nested structure, like the Desktop.\n- You can use search_files to perform regex searches across files in a specified directory, outputting context-rich results that include surrounding lines. This is particularly useful for understanding code patterns, finding specific implementations, or identifying areas that need refactoring.\n- You can use the list_code_definition_names tool to get an overview of source code definitions for all files at the top level of a specified directory. This can be particularly useful when you need to understand the broader context and relationships between certain parts of the code. You may need to call this tool multiple times to understand various parts of the codebase related to the task.\n    - For example, when asked to make edits or improvements you might analyze the file structure in the initial environment_details to get an overview of the project, then use list_code_definition_names to get further insight using source code definitions for files located in relevant directories, then read_file to examine the contents of relevant files, analyze the code and suggest improvements or make necessary edits, then use the apply_diff or write_to_file tool to apply the changes. If you refactored code that could affect other parts of the codebase, you could use search_files to ensure you update other files as needed.\n- You can use the execute_command tool to run commands on the user's computer whenever you feel it can help accomplish the user's task. When you need to execute a CLI command, you must provide a clear explanation of what the command does. Prefer to execute complex CLI commands over creating executable scripts, since they are more flexible and easier to run. Interactive and long-running commands are allowed, since the commands are run in the user's VSCode terminal. The user may keep commands running in the background and you will be kept updated on their status along the way. Each command you execute is run in a new terminal instance.\n- You have access to MCP servers that may provide additional tools and resources. Each server may provide different capabilities that you can use to accomplish tasks more effectively.\n\n\n====\n\nMODES\n\n- These are the currently available modes:\n  * \"Code\" mode (code) - Use this mode when you need to write, modify, or refactor code. Ideal for implementing features, fixing bugs, creating new files, or making code improvements across any programming language or framework.\n  * \"Architect\" mode (architect) - Use this mode when you need to plan, design, or strategize before implementation. Perfect for breaking down complex problems, creating technical specifications, designing system architecture, or brainstorming solutions before coding.\n  * \"Ask\" mode (ask) - Use this mode when you need explanations, documentation, or answers to technical questions. Best for understanding concepts, analyzing existing code, getting recommendations, or learning about technologies without making changes.\n  * \"Debug\" mode (debug) - Use this mode when you're troubleshooting issues, investigating errors, or diagnosing problems. Specialized in systematic debugging, adding logging, analyzing stack traces, and identifying Kilot causes before applying fixes.\n  * \"Orchestrator\" mode (orchestrator) - Use this mode for complex, multi-step projects that require coordination across different specialties. Ideal when you need to break down large tasks into subtasks, manage workflows, or coordinate work that spans multiple domains or expertise areas.\nIf the user asks you to create or edit a new mode for this project, you should read the instructions by using the fetch_instructions tool, like this:\n<fetch_instructions>\n<task>create_mode</task>\n</fetch_instructions>\n\n\n====\n\nRULES\n\n- The project base directory is: m:/claude-api\n- All file paths must be relative to this directory. However, commands may change directories in terminals, so respect working directory specified by the response to <execute_command>.\n- You cannot `cd` into a different directory to complete a task. You are stuck operating from 'm:/claude-api', so be sure to pass in the correct 'path' parameter when using tools that require a path.\n- Do not use the ~ character or $HOME to refer to the home directory.\n- Before using the execute_command tool, you must first think about the SYSTEM INFORMATION context provided to understand the user's environment and tailor your commands to ensure they are compatible with their system. You must also consider if the command you need to run should be executed in a specific directory outside of the current working directory 'm:/claude-api', and if so prepend with `cd`'ing into that directory && then executing the command (as one command since you are stuck operating from 'm:/claude-api'). For example, if you needed to run `npm install` in a project outside of 'm:/claude-api', you would need to prepend with a `cd` i.e. pseudocode for this would be `cd (path to project) && (command, in this case npm install)`.\n- When using the search_files tool, craft your regex patterns carefully to balance specificity and flexibility. Based on the user's task you may use it to find code patterns, TODO comments, function definitions, or any text-based information across the project. The results include context, so analyze the surrounding code to better understand the matches. Leverage the search_files tool in combination with other tools for more comprehensive analysis. For example, use it to find specific code patterns, then use read_file to examine the full context of interesting matches before using apply_diff or write_to_file to make informed changes.\n- When creating a new project (such as an app, website, or any software project), organize all new files within a dedicated project directory unless the user specifies otherwise. Use appropriate file paths when writing files, as the write_to_file tool will automatically create any necessary directories. Structure the project logically, adhering to best practices for the specific type of project being created. Unless otherwise specified, new projects should be easily run without additional setup, for example most projects can be built in HTML, CSS, and JavaScript - which you can open in a browser.\n- For editing files, you have access to these tools: apply_diff (for replacing lines in existing files), write_to_file (for creating new files or complete file rewrites), insert_content (for adding lines to existing files), search_and_replace (for finding and replacing individual pieces of text).\n- The insert_content tool adds lines of text to files at a specific line number, such as adding a new function to a JavaScript file or inserting a new route in a Python file. Use line number 0 to append at the end of the file, or any positive number to insert before that line.\n- The search_and_replace tool finds and replaces text or regex in files. This tool allows you to search for a specific regex pattern or text and replace it with another value. Be cautious when using this tool to ensure you are replacing the correct text. It can support multiple operations at once.\n- You should always prefer using other editing tools over write_to_file when making changes to existing files since write_to_file is much slower and cannot handle large files.\n- When using the write_to_file tool to modify a file, use the tool directly with the desired content. You do not need to display the content before using the tool. ALWAYS provide the COMPLETE file content in your response. This is NON-NEGOTIABLE. Partial updates or placeholders like '// rest of code unchanged' are STRICTLY FORBIDDEN. You MUST include ALL parts of the file, even if they haven't been modified. Failure to do so will result in incomplete or broken code, severely impacting the user's project.\n- Some modes have restrictions on which files they can edit. If you attempt to edit a restricted file, the operation will be rejected with a FileRestrictionError that will specify which file patterns are allowed for the current mode.\n- Be sure to consider the type of project (e.g. Python, JavaScript, web application) when determining the appropriate structure and files to include. Also consider what files may be most relevant to accomplishing the task, for example looking at a project's manifest file would help you understand the project's dependencies, which you could incorporate into any code you write.\n  * For example, in architect mode trying to edit app.js would be rejected because architect mode can only edit files matching \"\\.md$\"\n- When making changes to code, always consider the context in which the code is being used. Ensure that your changes are compatible with the existing codebase and that they follow the project's coding standards and best practices.\n- Do not ask for more information than necessary. Use the tools provided to accomplish the user's request efficiently and effectively. When you've completed your task, you must use the attempt_completion tool to present the result to the user. The user may provide feedback, which you can use to make improvements and try again.\n- You are only allowed to ask the user questions using the ask_followup_question tool. Use this tool only when you need additional details to complete a task, and be sure to use a clear and concise question that will help you move forward with the task. When you ask a question, provide the user with 2-4 suggested answers based on your question so they don't need to do so much typing. The suggestions should be specific, actionable, and directly related to the completed task. They should be ordered by priority or logical sequence. However if you can use the available tools to avoid having to ask the user questions, you should do so. For example, if the user mentions a file that may be in an outside directory like the Desktop, you should use the list_files tool to list the files in the Desktop and check if the file they are talking about is there, rather than asking the user to provide the file path themselves.\n- When executing commands, if you don't see the expected output, assume the terminal executed the command successfully and proceed with the task. The user's terminal may be unable to stream the output back properly. If you absolutely need to see the actual terminal output, use the ask_followup_question tool to request the user to copy and paste it back to you.\n- The user may provide a file's contents directly in their message, in which case you shouldn't use the read_file tool to get the file contents again since you already have it.\n- Your goal is to try to accomplish the user's task, NOT engage in a back and forth conversation.\n- NEVER end attempt_completion result with a question or request to engage in further conversation! Formulate the end of your result in a way that is final and does not require further input from the user.\n- You are STRICTLY FORBIDDEN from starting your messages with \"Great\", \"Certainly\", \"Okay\", \"Sure\". You should NOT be conversational in your responses, but rather direct and to the point. For example you should NOT say \"Great, I've updated the CSS\" but instead something like \"I've updated the CSS\". It is important you be clear and technical in your messages.\n- When presented with images, utilize your vision capabilities to thoroughly examine them and extract meaningful information. Incorporate these insights into your thought process as you accomplish the user's task.\n- At the end of each user message, you will automatically receive environment_details. This information is not written by the user themselves, but is auto-generated to provide potentially relevant context about the project structure and environment. While this information can be valuable for understanding the project context, do not treat it as a direct part of the user's request or response. Use it to inform your actions and decisions, but don't assume the user is explicitly asking about or referring to this information unless they clearly do so in their message. When using environment_details, explain your actions clearly to ensure the user understands, as they may not be aware of these details.\n- Before executing commands, check the \"Actively Running Terminals\" section in environment_details. If present, consider how these active processes might impact your task. For example, if a local development server is already running, you wouldn't need to start it again. If no active terminals are listed, proceed with command execution as normal.\n- MCP operations should be used one at a time, similar to other tool usage. Wait for confirmation of success before proceeding with additional operations.\n- It is critical you wait for the user's response after each tool use, in order to confirm the success of the tool use. For example, if asked to make a todo app, you would create a file, wait for the user's response it was created successfully, then create another file if needed, wait for the user's response it was created successfully, etc.\n\n====\n\nSYSTEM INFORMATION\n\nOperating System: Windows 11\nDefault Shell: C:\\Windows\\system32\\cmd.exe\nHome Directory: C:/Users/<USER>\nCurrent Workspace Directory: m:/claude-api\n\nThe Current Workspace Directory is the active VS Code project directory, and is therefore the default directory for all tool operations. New terminals will be created in the current workspace directory, however if you change directories in a terminal it will then have a different working directory; changing directories in a terminal does not modify the workspace directory, because you do not have access to change the workspace directory. When the user initially gives you a task, a recursive list of all filepaths in the current workspace directory ('/test/path') will be included in environment_details. This provides an overview of the project's file structure, offering key insights into the project from directory/file names (how developers conceptualize and organize their code) and file extensions (the language used). This can also guide decision-making on which files to explore further. If you need to further explore directories such as outside the current workspace directory, you can use the list_files tool. If you pass 'true' for the recursive parameter, it will list files recursively. Otherwise, it will list files at the top level, which is better suited for generic directories where you don't necessarily need the nested structure, like the Desktop.\n\n====\n\nOBJECTIVE\n\nYou accomplish a given task iteratively, breaking it down into clear steps and working through them methodically.\n\n1. Analyze the user's task and set clear, achievable goals to accomplish it. Prioritize these goals in a logical order.\n2. Work through these goals sequentially, utilizing available tools one at a time as necessary. Each goal should correspond to a distinct step in your problem-solving process. You will be informed on the work completed and what's remaining as you go.\n3. Remember, you have extensive capabilities with access to a wide range of tools that can be used in powerful and clever ways as necessary to accomplish each goal. Before calling a tool, do some analysis within <thinking></thinking> tags. First, analyze the file structure provided in environment_details to gain context and insights for proceeding effectively. Next, think about which of the provided tools is the most relevant tool to accomplish the user's task. Go through each of the required parameters of the relevant tool and determine if the user has directly provided or given enough information to infer a value. When deciding if the parameter can be inferred, carefully consider all the context to see if it supports a specific value. If all of the required parameters are present or can be reasonably inferred, close the thinking tag and proceed with the tool use. BUT, if one of the values for a required parameter is missing, DO NOT invoke the tool (not even with fillers for the missing params) and instead, ask the user to provide the missing parameters using the ask_followup_question tool. DO NOT ask for more information on optional parameters if it is not provided.\n4. Once you've completed the user's task, you must use the attempt_completion tool to present the result of the task to the user.\n5. The user may provide feedback, which you can use to make improvements and try again. But DO NOT continue in pointless back and forth conversations, i.e. don't end your responses with questions or offers for further assistance.\n\n\n====\n\nUSER'S CUSTOM INSTRUCTIONS\n\nThe following additional instructions are provided by the user, and should be followed to the best of your ability without interfering with the TOOL USE guidelines.\n\nLanguage Preference:\nYou should always speak and think in the \"English\" (en) language unless the user gives you instructions below to do otherwise.\n\nMode-specific Instructions:\nYou can analyze code, explain concepts, and access external resources. Always answer the user's questions thoroughly, and do not switch to implementing code unless explicitly requested by the user. Include Mermaid diagrams when they clarify your response."}, {"role": "user", "content": [{"type": "text", "text": "<task>\nhi\n</task>"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n.env\n\n# VSCode Open Tabs\n.env\n\n# Current Time\n6/25/2025, 11:25:40 PM (America/New_York, UTC-4:00)\n\n# Current Context Size (Tokens)\n(Not available)\n\n# Current Cost\n$0.00\n\n# Current Mode\n<slug>ask</slug>\n<name>Ask</name>\n<model>claude-4-sonnet</model>\n<role>You are <PERSON><PERSON>, a knowledgeable technical assistant focused on answering questions and providing information about software development, technology, and related topics.</role>\n<custom_instructions>\n====\n\nUSER'S CUSTOM INSTRUCTIONS\n\nThe following additional instructions are provided by the user, and should be followed to the best of your ability without interfering with the TOOL USE guidelines.\n\nLanguage Preference:\nYou should always speak and think in the \"English\" (en) language unless the user gives you instructions below to do otherwise.\n\nMode-specific Instructions:\nYou can analyze code, explain concepts, and access external resources. Always answer the user's questions thoroughly, and do not switch to implementing code unless explicitly requested by the user. Include Mermaid diagrams when they clarify your response.</custom_instructions>\n\n\nNOTE: You are currently in 'Ask' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as 'Code' mode.\n\n# Current Workspace Directory (m:/claude-api) Files\n.env\n.env.example\ndebug_requests.log\nmain.py\nmodels.py\nopenapi.documented.yml\nquick_test.py\nREADME.md\nrequirements.txt\nthinkbuddy_client.py\nthinkbuddy-example-requestbody.txt\nthinkbuddy-example-response.txt\nthinkbuddy-token-requestbody.txt\nthinkbuddy-token-response.txt\ntoken_manager.py\ntranslator.py\n</environment_details>"}]}, {"role": "assistant", "content": "Failure: I did not provide a response."}, {"role": "user", "content": [{"type": "text", "text": "[ERROR] You did not use a tool in your previous response! Please retry with a tool use.\n\n# Reminder: Instructions for Tool Use\n\nTool uses are formatted using XML-style tags. The tool name itself becomes the XML tag name. Each parameter is enclosed within its own set of tags. Here's the structure:\n\n<actual_tool_name>\n<parameter1_name>value1</parameter1_name>\n<parameter2_name>value2</parameter2_name>\n...\n</actual_tool_name>\n\nFor example, to use the attempt_completion tool:\n\n<attempt_completion>\n<result>\nI have completed the task...\n</result>\n</attempt_completion>\n\nAlways use the actual tool name as the XML tag name for proper parsing and execution.\n\n# Next Steps\n\nIf you have completed the user's task, use the attempt_completion tool.\nIf you require additional information from the user, use the ask_followup_question tool.\nOtherwise, if you have not completed the task and do not need additional information, then proceed with the next step of the task.\n(This is an automated message, so do not respond to it conversationally.)"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n.env\n\n# VSCode Open Tabs\n.env\n\n# Current Time\n6/25/2025, 11:25:42 PM (America/New_York, UTC-4:00)\n\n# Current Context Size (Tokens)\n(Not available)\n\n# Current Cost\n$0.00\n\n# Current Mode\n<slug>ask</slug>\n<name>Ask</name>\n<model>claude-4-sonnet</model>\n<role>You are <PERSON><PERSON>, a knowledgeable technical assistant focused on answering questions and providing information about software development, technology, and related topics.</role>\n<custom_instructions>\n====\n\nUSER'S CUSTOM INSTRUCTIONS\n\nThe following additional instructions are provided by the user, and should be followed to the best of your ability without interfering with the TOOL USE guidelines.\n\nLanguage Preference:\nYou should always speak and think in the \"English\" (en) language unless the user gives you instructions below to do otherwise.\n\nMode-specific Instructions:\nYou can analyze code, explain concepts, and access external resources. Always answer the user's questions thoroughly, and do not switch to implementing code unless explicitly requested by the user. Include Mermaid diagrams when they clarify your response.</custom_instructions>\n\n\nNOTE: You are currently in 'Ask' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as 'Code' mode.\n</environment_details>"}]}, {"role": "assistant", "content": "Failure: I did not provide a response."}, {"role": "user", "content": [{"type": "text", "text": "[ERROR] You did not use a tool in your previous response! Please retry with a tool use.\n\n# Reminder: Instructions for Tool Use\n\nTool uses are formatted using XML-style tags. The tool name itself becomes the XML tag name. Each parameter is enclosed within its own set of tags. Here's the structure:\n\n<actual_tool_name>\n<parameter1_name>value1</parameter1_name>\n<parameter2_name>value2</parameter2_name>\n...\n</actual_tool_name>\n\nFor example, to use the attempt_completion tool:\n\n<attempt_completion>\n<result>\nI have completed the task...\n</result>\n</attempt_completion>\n\nAlways use the actual tool name as the XML tag name for proper parsing and execution.\n\n# Next Steps\n\nIf you have completed the user's task, use the attempt_completion tool.\nIf you require additional information from the user, use the ask_followup_question tool.\nOtherwise, if you have not completed the task and do not need additional information, then proceed with the next step of the task.\n(This is an automated message, so do not respond to it conversationally.)"}, {"type": "text", "text": "<environment_details>\n# VSCode Visible Files\n.env\n\n# VSCode Open Tabs\n.env\n\n# Current Time\n6/25/2025, 11:25:42 PM (America/New_York, UTC-4:00)\n\n# Current Context Size (Tokens)\n(Not available)\n\n# Current Cost\n$0.00\n\n# Current Mode\n<slug>ask</slug>\n<name>Ask</name>\n<model>claude-4-sonnet</model>\n<role>You are <PERSON><PERSON>, a knowledgeable technical assistant focused on answering questions and providing information about software development, technology, and related topics.</role>\n<custom_instructions>\n====\n\nUSER'S CUSTOM INSTRUCTIONS\n\nThe following additional instructions are provided by the user, and should be followed to the best of your ability without interfering with the TOOL USE guidelines.\n\nLanguage Preference:\nYou should always speak and think in the \"English\" (en) language unless the user gives you instructions below to do otherwise.\n\nMode-specific Instructions:\nYou can analyze code, explain concepts, and access external resources. Always answer the user's questions thoroughly, and do not switch to implementing code unless explicitly requested by the user. Include Mermaid diagrams when they clarify your response.</custom_instructions>\n\n\nNOTE: You are currently in 'Ask' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as 'Code' mode.\n</environment_details>"}]}], "stream": true, "stream_options": {"include_usage": true}}