"""
Translation logic between OpenAI and ThinkBuddy API formats
"""
import json
import time
import uuid
import logging
from typing import Dict, Any, List, Optional
from models import (
    ChatCompletionRequest, ChatCompletionResponse, ChatCompletionChoice,
    ChatCompletionStreamResponse, ChatCompletionStreamChoice, ChatCompletionUsage,
    ThinkBuddyRequest, ThinkBuddyResponse, ThinkBuddyMessage,
    ChatMessage
)

logger = logging.getLogger(__name__)


class APITranslator:
    """Handles translation between OpenAI and ThinkBuddy API formats"""

    # Model mapping - easy to extend with more models in the future
    MODEL_MAPPING = {
        "claude-4-sonnet": "claude-4-sonnet",
        # Add more model mappings here as needed:
        # "claude-3-opus": "claude-3-opus",
        # "claude-3-sonnet": "claude-3-sonnet",
        # "claude-3-haiku": "claude-3-haiku",
    }

    def __init__(self, default_model: str = "claude-4-sonnet"):
        self.default_model = default_model

    def openai_to_thinkbuddy(self, openai_request: ChatCompletionRequest) -> ThinkBuddyRequest:
        """Convert OpenAI ChatCompletionRequest to ThinkBuddy format"""

        # Map model name
        thinkbuddy_model = self.MODEL_MAPPING.get(openai_request.model, self.default_model)

        # Convert messages
        thinkbuddy_messages = []
        for msg in openai_request.messages:
            # ThinkBuddy expects simple role/content format
            thinkbuddy_msg = ThinkBuddyMessage(
                role=msg.role,
                content=msg.content or ""
            )
            thinkbuddy_messages.append(thinkbuddy_msg)

        # Create ThinkBuddy request
        thinkbuddy_request = ThinkBuddyRequest(
            model=thinkbuddy_model,
            messages=thinkbuddy_messages,
            temperature=openai_request.temperature,
            top_p=openai_request.top_p,
            stream=openai_request.stream
        )

        logger.debug(f"Translated OpenAI request to ThinkBuddy: {thinkbuddy_request.model_dump()}")
        return thinkbuddy_request

    def thinkbuddy_to_openai(
        self,
        thinkbuddy_response: ThinkBuddyResponse,
        original_request: ChatCompletionRequest
    ) -> ChatCompletionResponse:
        """Convert ThinkBuddy response to OpenAI ChatCompletionResponse format"""

        # Generate OpenAI-compatible ID if not provided
        response_id = thinkbuddy_response.id or f"chatcmpl-{uuid.uuid4().hex[:29]}"

        # Convert choices
        openai_choices = []
        for i, choice in enumerate(thinkbuddy_response.choices):
            openai_message = ChatMessage(
                role=choice.message.role,
                content=choice.message.content
            )

            openai_choice = ChatCompletionChoice(
                index=i,
                message=openai_message,
                finish_reason=choice.finish_reason or "stop"
            )
            openai_choices.append(openai_choice)

        # Convert usage if available
        usage = None
        if thinkbuddy_response.usage:
            usage = ChatCompletionUsage(
                prompt_tokens=thinkbuddy_response.usage.get("prompt_tokens", 0),
                completion_tokens=thinkbuddy_response.usage.get("completion_tokens", 0),
                total_tokens=thinkbuddy_response.usage.get("total_tokens", 0)
            )

        # Create OpenAI response
        openai_response = ChatCompletionResponse(
            id=response_id,
            created=thinkbuddy_response.created or int(time.time()),
            model=original_request.model,  # Return the original model name requested
            choices=openai_choices,
            usage=usage
        )

        logger.debug(f"Translated ThinkBuddy response to OpenAI: {openai_response.model_dump()}")
        return openai_response

    def create_stream_chunk(
        self,
        content: str,
        original_request: ChatCompletionRequest,
        chunk_id: Optional[str] = None,
        finish_reason: Optional[str] = None
    ) -> str:
        """Create an OpenAI-compatible streaming chunk"""

        if chunk_id is None:
            chunk_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"

        # Create delta message
        delta = ChatMessage(role="assistant", content=content)

        # Create stream choice
        choice = ChatCompletionStreamChoice(
            index=0,
            delta=delta,
            finish_reason=finish_reason
        )

        # Create stream response
        stream_response = ChatCompletionStreamResponse(
            id=chunk_id,
            model=original_request.model,
            choices=[choice]
        )

        # Format as SSE
        chunk_json = stream_response.model_dump_json()
        return f"data: {chunk_json}\n\n"

    def create_stream_done_chunk(self) -> str:
        """Create the final [DONE] chunk for streaming"""
        return "data: [DONE]\n\n"

    def parse_thinkbuddy_stream_chunk(self, chunk_data: str) -> Optional[Dict[str, Any]]:
        """Parse a chunk from ThinkBuddy streaming response"""
        try:
            if not chunk_data.strip():
                return None

            # Try to parse as JSON
            chunk_json = json.loads(chunk_data)
            return chunk_json

        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse ThinkBuddy stream chunk: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error parsing stream chunk: {e}")
            return None

    def extract_content_from_chunk(self, chunk: Dict[str, Any]) -> Optional[str]:
        """Extract content from a ThinkBuddy streaming chunk"""
        try:
            # ThinkBuddy format: {"choices":[{"delta":{"content":"text"}}]}
            if "choices" in chunk and len(chunk["choices"]) > 0:
                choice = chunk["choices"][0]
                if "delta" in choice and "content" in choice["delta"]:
                    return choice["delta"]["content"]

            return None

        except Exception as e:
            logger.warning(f"Failed to extract content from chunk: {e}")
            return None
